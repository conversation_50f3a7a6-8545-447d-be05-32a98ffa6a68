Set<String> emails = new Set<String>{

};

Set<Account> accounts = new Set<Account>();
Integer nu = 0;
for(Account temp : [SELECT id, PersonEmail from Account where RecordType.Name = 'End User Account' and PersonEmail IN: emails]){
    if(temp.PersonEmail != null && temp.PersonEmail != '' && emails.contains(temp.PersonEmail) ){
        accounts.add(temp);
        nu++;
    }
}

for(Account a : accounts){
    System.debug(a.id + ' ' + a.PersonEmail);
}
System.debug('********'+nu);