@IsTest
private class CCM_Community_FleetEditCtlTest {
    @TestSetup
    static void testSetup(){
        Test.startTest();
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != NULL AND IsActive = TRUE LIMIT 1];
        Profile profilePartner = [
            SELECT Id 
            FROM Profile 
            WHERE Name = 'Partner Community Sales' 
            LIMIT 1
        ];

        User userPartner;
        System.runAs(admin){
            Account customer = new Account();
            customer.Name = 'Test Customer';
            customer.AccountNumber = '*********';
            customer.TaxID__c = 'test';
            insert customer;

            Contact con = new Contact();
            con.FirstName = 'test';
            con.LastName = 'Contact';
            con.AccountId = customer.Id;
            con.Phone = '*********0';
            con.Email = System.now().getTime() + '@test.com';
            insert con;

            userPartner = new User(
                Email = '<EMAIL>',
                ProfileId = profilePartner.Id,
                Username = '<EMAIL>',
                Alias = 'Test',
                TimeZoneSidKey = 'America/New_York',
                EmailEncodingKey = 'ISO-8859-1',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US',
                ContactId = con.Id,
                FirstName = 'Firstname',
                LastName = 'Lastname'
            );

            insert userPartner;

            Fleet_Program_Rule__c rule = new Fleet_Program_Rule__c();
            rule.Deliver_At_Once__c = true;
            rule.Maximum_Discount_Criteria__c = 15;
            rule.Discount_Return__c = 7.5;
            rule.Year__c = String.valueOf(Date.today().year());

            insert rule;

            Fleet_Program_Target_Customer__c tc = new Fleet_Program_Target_Customer__c();
            tc.Fleet_Program_Rule__c = rule.id;
            tc.Customer__c = customer.id;
            insert tc;

            Product2 pro = new Product2();
            pro.Name = 'Test';
            pro.Brand_Name__c = 'EGO';
            pro.RecordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
            pro.ProductCode = '1234567';
            pro.IsActive = true;
            pro.Source__c = 'PIM';
            pro.Country_of_Origin__c = 'United States';
            insert pro;

            Product2 pro2 = new Product2();
            pro2.Name = 'Test';
            pro2.Brand_Name__c = 'EGO';
            pro2.RecordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
            pro2.ProductCode = '12345670';
            pro2.IsActive = true;
            pro2.Source__c = 'PIM';
            pro2.Country_of_Origin__c = 'United States';
            insert pro2;

            Product2 pro3 = new Product2();
            pro3.Name = 'Test';
            pro3.Brand_Name__c = 'EGO';
            pro3.RecordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
            pro3.ProductCode = '12345670';
            pro3.IsActive = true;
            pro3.Source__c = 'PIM';
            pro3.Country_of_Origin__c = 'United States';
            insert pro3;
        
            Product2 pkit = new Product2();
            pkit.Name = 'Test';
            pkit.Brand_Name__c = 'EGO';
            pkit.RecordTypeId = CCM_Constants.PRODUCT_KIT_RECORD_TYPE_ID;
            pkit.ProductCode = '1234567';
            pkit.IsActive = true;
            pkit.Source__c = 'PIM';
            pkit.Country_of_Origin__c = 'United States';
            insert pkit;

            Product2 pkit2 = new Product2();
            pkit2.Name = 'Test';
            pkit2.Brand_Name__c = 'EGO';
            pkit2.RecordTypeId = CCM_Constants.PRODUCT_KIT_RECORD_TYPE_ID;
            pkit2.ProductCode = '1234567---';
            pkit2.IsActive = true;
            pkit2.Source__c = 'PIM';
            pkit2.Country_of_Origin__c = 'United States';
            insert pkit2;

            Program_Rule_Product_Relationship__c relationship = new Program_Rule_Product_Relationship__c();
            relationship.Fleet_Program_Rule__c = rule.Id;
            relationship.Kit__c = pkit.Id;
            insert relationship;

            Program_Rule_Product_Relationship__c relationship2 = new Program_Rule_Product_Relationship__c();
            relationship2.Fleet_Program_Rule__c = rule.Id;
            relationship2.Kit__c = pkit2.Id;
            insert relationship2;

            Kit_Item__c item = new Kit_Item__c();
            item.Kit__c = pkit.Id;
            item.Product__c = pro.Id;
            item.Status__c = 'A';
            item.Sequence__c = 'xxxx';
            insert item;

            Kit_Item__c item2 = new Kit_Item__c();
            item2.Kit__c = pkit.Id;
            item2.Product__c = pro2.Id;
            item2.Status__c = 'A';
            item2.Sequence__c = 'xxxx';
            insert item2;

            Kit_Item__c item3 = new Kit_Item__c();
            item3.Kit__c = pkit.Id;
            item3.Product__c = pro3.Id;
            item3.Status__c = 'A';
            item3.Sequence__c = 'xxxx';
            insert item3;

            Pricebook2 pb = new Pricebook2();
            pb.IsActive = true;
            pb.Name = 'CNA-EGO-MSRP';
            insert pb;

            PricebookEntry pbe = new PricebookEntry();
            pbe.IsActive = true;
            pbe.Product2Id = pkit2.Id;
            pbe.UnitPrice = 1000;
            pbe.Pricebook2Id = pb.Id;
            pbe.UseStandardPrice = false;
            insert pbe;
        }
        Test.stopTest();
    }
    @IsTest
    static void testMethod1() {
        
        List<User> userPartner = [
            SELECT Id
            FROM User
            WHERE Username = '<EMAIL>'
        ];
        AuraResponseEntity aura = CCM_Community_FleetEditCtl.getInitData(userPartner[0].Id, null);

        List<Product2> kitList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '1234567'
        ];

        List<Product2> proList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '12345670'
        ];

        Map<String, Object> dataMap = (Map<String, Object>)aura.data;
        CCM_Community_FleetEditCtl.FleetClaim fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)dataMap.get('fleetClaim');
        fleetClaim.fleetItemList = new List<CCM_Community_FleetEditCtl.FleetItem>();
        fleetClaim.fleetItemList.add(new CCM_Community_FleetEditCtl.FleetItem());
        fleetClaim.fleetItemList[0].qtyPurchased = 1;
        fleetClaim.fleetItemList[0].warrantyList = new List<CCM_Community_FleetEditCtl.Warranty>();

        Test.startTest();
        CCM_Community_FleetEditCtl.WarrantyItem warrantyItem = new CCM_Community_FleetEditCtl.WarrantyItem();
        warrantyItem.fakeId = 'aaaaaaaaaaaaaaaaaaa';
        warrantyItem.kitId = kitList[0].Id;
        warrantyItem.productId = proList[0].Id;
        warrantyItem.serialNumber = '***********';

        CCM_Community_FleetEditCtl.Warranty warranty = new CCM_Community_FleetEditCtl.Warranty();
        warranty.fakeId = 'xxxxxxxxxxxxxxxxxxxx';
        warranty.kitId = kitList[0].Id;
        warranty.warrantyItemList = new List<CCM_Community_FleetEditCtl.WarrantyItem>();
        warranty.warrantyItemList.add(warrantyItem);

        fleetClaim.fleetItemList[0].warrantyList.add(warranty);

        fleetClaim.endUserCustomer = new CCM_Community_FleetEditCtl.AccountInfo();
        fleetClaim.endUserCustomer.lastName = 'wang';
        fleetClaim.endUserCustomer.firstName = 'test';
        fleetClaim.endUserCustomer.emailAddress = '<EMAIL>';

        aura = CCM_Community_FleetEditCtl.saveFleetClaim(JSON.serialize(fleetClaim), 'Save');
        fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)aura.data;
        CCM_Community_FleetEditCtl.getFleetClaimData(fleetClaim.id);
        CCM_Community_FleetEditCtl.checkSNAndUpdateIndicator(JSON.serialize(fleetClaim.fleetItemList));
        CCM_Community_FleetEditCtl.getCustomerInfo('<EMAIL>');
        Test.stopTest();
    }
    @IsTest
    static void testMethod2() {
        Test.startTest();
        List<User> userPartner = [
            SELECT Id
            FROM User
            WHERE Username = '<EMAIL>'
        ];
        AuraResponseEntity aura = CCM_Community_FleetEditCtl.getInitData(userPartner[0].Id, null);

        List<Product2> kitList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '1234567'
        ];

        List<Product2> proList = [
            SELECT Id
            FROM Product2
            WHERE ProductCode = '12345670'
        ];

        Map<String, Object> dataMap = (Map<String, Object>)aura.data;
        CCM_Community_FleetEditCtl.FleetClaim fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)dataMap.get('fleetClaim');
        fleetClaim.fleetItemList = new List<CCM_Community_FleetEditCtl.FleetItem>();
        fleetClaim.fleetItemList.add(new CCM_Community_FleetEditCtl.FleetItem());
        fleetClaim.fleetItemList[0].qtyPurchased = 1;
        fleetClaim.fleetItemList[0].warrantyList = new List<CCM_Community_FleetEditCtl.Warranty>();


        CCM_Community_FleetEditCtl.WarrantyItem warrantyItem = new CCM_Community_FleetEditCtl.WarrantyItem();
        warrantyItem.fakeId = 'aaaaaaaaaaaaaaaaaaa';
        warrantyItem.kitId = kitList[0].Id;
        warrantyItem.productId = proList[0].Id;
        warrantyItem.serialNumber = '***********';

        CCM_Community_FleetEditCtl.Warranty warranty = new CCM_Community_FleetEditCtl.Warranty();
        warranty.fakeId = 'xxxxxxxxxxxxxxxxxxxx';
        warranty.kitId = kitList[0].Id;
        warranty.warrantyItemList = new List<CCM_Community_FleetEditCtl.WarrantyItem>();
        warranty.warrantyItemList.add(warrantyItem);

        fleetClaim.fleetItemList[0].warrantyList.add(warranty);

        fleetClaim.endUserCustomer = new CCM_Community_FleetEditCtl.AccountInfo();
        fleetClaim.endUserCustomer.lastName = 'wang';
        fleetClaim.endUserCustomer.firstName = 'test';
        fleetClaim.endUserCustomer.emailAddress = '<EMAIL>';

        aura = CCM_Community_FleetEditCtl.saveFleetClaim(JSON.serialize(fleetClaim), 'Save');
        fleetClaim = (CCM_Community_FleetEditCtl.FleetClaim)aura.data;
        CCM_Community_FleetEditCtl.uploadFile(fleetClaim.id, 'XXXX', 'XXXXXXXX');
        CCM_Community_FleetEditCtl.deleteFile('XXXXXXXX');
        // List<Account> accList =[SELECT id from account limit 1];
        // CCM_Community_FleetEditCtl.hasOneClaimMatchAmountThreshold(accList[0].id,fleetClaim.Id);
        // Set<Id> deleteIdSet = new Set<Id>();
        // CCM_Community_FleetEditCtl.deleteNotExistWarrantyByFleetClaimItemIds(deleteIdSet);
        Test.stopTest();
    }
    @IsTest
    static void testVerifySerialNumber() {
        Test.startTest();
        System.assertEquals(false, CCM_Community_FleetEditCtl.verifySerialNumber(null, null, null, null));
        System.assertEquals(true, CCM_Community_FleetEditCtl.verifySerialNumber('EGO', '123', new Map<String, System_Configuration__c>(), null));
        System.assertEquals(true, CCM_Community_FleetEditCtl.verifySerialNumber('EGO', '123', new Map<String, System_Configuration__c>{
            'EGO_v0' => new System_Configuration__c(
                Name = 'EGO_v0',
                RegExp__c = '[0-9]{3}'
            )
        }, null));
        System.assertEquals(false, CCM_Community_FleetEditCtl.verifySerialNumber('EGO', 'abc', new Map<String, System_Configuration__c>{
            'EGO_v0' => new System_Configuration__c(
                Name = 'EGO_v0',
                RegExp__c = '[0-9]{3}'
            )
        }, null));
        Test.stopTest();
    }

    @IsTest
    static void testCheckSerialNumber(){
        Test.startTest();
        CCM_Community_FleetEditCtl.WarrantyItem item = new CCM_Community_FleetEditCtl.WarrantyItem();
        item.serialNumber = '123';
        item.kitBrand = 'Skil';
        CCM_Community_FleetEditCtl.checkSerialNumber(item, new Map<String, Warranty_Rules__c>(), new Map<String,System_Configuration__c>(), new List<Project_SN__c>());

        item = new CCM_Community_FleetEditCtl.WarrantyItem();
        item.serialNumber = '*********';
        item.kitBrand = 'SkilSaw';
        CCM_Community_FleetEditCtl.checkSerialNumber(item, new Map<String, Warranty_Rules__c>(), new Map<String,System_Configuration__c>(), new List<Project_SN__c>());


        Warranty_Rules__c rule = new Warranty_Rules__c();
        rule.NA_FC_Model__c = 'ABCD';
        rule.NA_Model__c = 'ABCD';
        insert rule;
        item = new CCM_Community_FleetEditCtl.WarrantyItem();
        item.serialNumber = '1ABCD6789101213';
        item.kitBrand = 'EGO';
        item.productCode = 'ABCD';
        CCM_Community_FleetEditCtl.checkSerialNumber(item, new Map<String, Warranty_Rules__c>{'ABCD'=>rule}, new Map<String,System_Configuration__c>(), new List<Project_SN__c>());
        
        item = new CCM_Community_FleetEditCtl.WarrantyItem();
        item.serialNumber = '11ABCD7891012134';
        item.kitBrand = 'EGO';
        item.productCode = 'ABCD';
        CCM_Community_FleetEditCtl.checkSerialNumber(item, new Map<String, Warranty_Rules__c>{'ABCD'=>rule}, new Map<String,System_Configuration__c>(), new List<Project_SN__c>());
        Test.stopTest();
    }
 
    @IsTest
    static void methodName(){
        Test.startTest();
        
        // CCM_Community_FleetEditCtl.getAddressInfo('12345', 'US');
        CCM_Community_FleetEditCtl.FleetClaim claim = new CCM_Community_FleetEditCtl.FleetClaim();
        claim.id = 'test';
        claim.name = 'test';
        claim.claimPackName = 'test';
        claim.fleetProgramRuleId = 'test';
        claim.approvalStatus = 'test';
        claim.isPaidLabel = 'test';
        claim.currencyCode = 'test';
        claim.salesDateStr = 'test';
        claim.brand = 'test';
        claim.lstPrimaryUseOption = new List<String>();
        claim.primaryUse = 'test';
        claim.fleetItemList = new List<CCM_Community_FleetEditCtl.FleetItem>();
        claim.totalSalesAmount = 10;
        claim.fleetDiscount = 10;
        claim.totalRetailPrice = 10;
        claim.estimatedCreditReturn = 10;
        claim.deliverAtOnce = false;
        claim.deliverAtOnceLabel = 'test';
        claim.billToAddressId = 'test';
        claim.billToAddressName = 'test';
        claim.addressIdListStr = 'test';
        claim.haveApprovalHistory = false;
        claim.invoiceNumber = 'test';

        CCM_Community_FleetEditCtl.AccountInfo info = new CCM_Community_FleetEditCtl.AccountInfo();
        info.id = 'test';
        info.name = 'test';
        info.lastName = 'test';
        info.firstName = 'test';
        info.country = 'test';
        info.zipPostalCode = 'test';
        info.orgName = 'test';
        info.phone = 'test';
        info.city = 'test';
        info.state = 'test';
        info.addressLine = 'test';
        info.emailAddress = 'test';
        info.productType = 'test';
        info.eligibleForFleet = false;
        info.source = 'test';

        CCM_Community_FleetEditCtl.FileInfo fileInfo = new CCM_Community_FleetEditCtl.FileInfo();
        fileInfo.contentId = 'test';
        fileInfo.uploadFileName = 'test';
        fileInfo.contentUrl = 'test';
        fileInfo.contentDocumentId = 'test';
        fileInfo.islinkedEntity = false;

        CCM_Community_FleetEditCtl.FleetItem fleetItem = new CCM_Community_FleetEditCtl.FleetItem();
        fleetItem.id = 'test';
        fleetItem.fakeId = 'test';
        fleetItem.kitId = 'test';
        fleetItem.kitCode = 'test';
        fleetItem.kitName = 'test';
        fleetItem.msrp = 10;
        fleetItem.qtyPurchased = 10;
        fleetItem.total = 10;
        fleetItem.fleetDiscount = 10;
        fleetItem.dealerRebate = 10;
        fleetItem.unitSalesPrice = 10;
        fleetItem.totalSalesPrice = 10;
        fleetItem.productType = 'test';
        
        CCM_Community_FleetEditCtl.Warranty warranty = new CCM_Community_FleetEditCtl.Warranty();
        warranty.index = 10;
        warranty.id = 'test';
        warranty.fakeId = 'test';
        warranty.kitId = 'test';
        warranty.kitCode = 'test';
        warranty.kitName = 'test';
        warranty.purchaseDate = 'test';
        warranty.createdByFleet = false;
        warranty.checked = false;

        CCM_Community_FleetEditCtl.WarrantyItem warrantyItem = new CCM_Community_FleetEditCtl.WarrantyItem();
        warrantyItem.id = 'test';
        warrantyItem.fakeId = 'test';
        warrantyItem.kitId = 'test';
        warrantyItem.kitCode = 'test';
        warrantyItem.kitName = 'test';
        warrantyItem.kitBrand = 'test';
        warrantyItem.productId = 'test';
        warrantyItem.productCode = 'test';
        warrantyItem.productName = 'test';
        warrantyItem.productModel = 'test';
        warrantyItem.productType = 'test';
        warrantyItem.sequence = 'test';
        WarrantyItem.haveSequenceProduct = false;
        warrantyItem.serialNumber = 'test';
        Test.stopTest();
    }
}