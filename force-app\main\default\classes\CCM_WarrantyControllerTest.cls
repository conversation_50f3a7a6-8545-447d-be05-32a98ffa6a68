@IsTest
private class CCM_WarrantyControllerTest {
    @TestSetup
    static void testDataSet() {
        Test.startTest();
        CCM_SharingUtil.isSharingOnly = true;
        List<System_Configuration__c> lstSystemConfiguration = new List<System_Configuration__c>{
            new System_Configuration__c(Name = 'Claim Auto Approval', Is_Active__c = true),
            new System_Configuration__c(
                Name = 'Hammerhead_v3',
                RegExp__c = '^[0-9](([13579][0-2])|([24680][1-9]))$',
                Is_Active__c = true,
                Enabled__c = true,
                RecordTypeId = CCM_Constants.SYSTEM_CONFIGURATION_RECORD_TYPE_SN_FORMAT_ID
            )
        };
        insert lstSystemConfiguration;
        Account acc = TestDataFactory.createAccount();
        acc.Product_Type__c = 'EGO';
        insert acc;
        Product2 p = TestDataFactory.createProduct();
        Warranty__c w = TestDataFactory.createWarranty();
        w.AccountCustomer__c = acc.Id;
        w.Master_Product__c = p.Id;
        w.Brand_Name__c = 'Skil';
        w.Product_Use_Type2__c = 'Professional/Commercial';
        w.Lost_Receipt__c = true;
        insert w;
        Warranty__c w1 = TestDataFactory.createWarranty();
        w1.AccountCustomer__c = acc.Id;
        w1.Master_Product__c = p.Id;
        w1.Brand_Name__c = 'SkilSaw';
        w1.Product_Use_Type2__c = 'Professional/Commercial';
        w1.Lost_Receipt__c = true;
        insert w1;
        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(w.Id);
        wi.Serial_Number__c = 'NLM01190500004';
        insert wi;
        Case cs = TestDataFactory.createCase();
        cs.AccountId = acc.Id;
        insert cs;
        List<Warranty_Item__c> items = new List<Warranty_Item__c>{ TestDataFactory.createWarrantyItem(w.Id), TestDataFactory.createWarrantyItem(w.Id) };
        insert items;
        Test.stopTest();
    }
    @IsTest
    //Add by Abby on 4/4/2020
    static void testChangeMasterProduct4() {
        List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Kit'];
        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Product'];
        RecordType rtd = [SELECT Id FROM RecordType WHERE Name = 'Products and Parts' AND SobjectType = 'Kit_item__c' LIMIT 1];
        Account acc = TestDataFactory.createAccount();
        acc.Product_Type__c = 'Skil';
        acc.Phone = '********';
        acc.PersonEmail = '<EMAIL>';
        insert acc;
        List<Product2> prodList = new List<Product2>();
        Product2 masterProd = new Product2();
        masterProd.Name = 'Test';
        masterProd.Brand_Name__c = 'Skil';
        masterProd.RecordTypeId = kitRecordType[0].Id;
        masterProd.ProductCode = 'CB738401';
        masterProd.Country_of_Origin__c = 'United States';
        masterProd.IsActive = true;
        masterProd.Source__c = 'EBS';
        prodList.add(masterProd);
        Product2 kitItemProd1 = new Product2();
        kitItemProd1.Name = 'Test';
        kitItemProd1.Brand_Name__c = 'Skil';
        kitItemProd1.RecordTypeId = proRecordType[0].Id;
        kitItemProd1.ProductCode = 'BY519901';
        kitItemProd1.Country_of_Origin__c = 'United States';
        kitItemProd1.IsActive = true;
        kitItemProd1.Source__c = 'EBS';
        prodList.add(kitItemProd1);
        Product2 kitItemProd2 = new Product2();
        kitItemProd2.Name = 'Test';
        kitItemProd2.Brand_Name__c = 'Skil';
        kitItemProd2.RecordTypeId = proRecordType[0].Id;
        kitItemProd2.ProductCode = 'SC536501';
        kitItemProd2.Country_of_Origin__c = 'United States';
        kitItemProd2.IsActive = true;
        kitItemProd2.Source__c = 'EBS';
        prodList.add(kitItemProd2);
        Product2 kitItemProd3 = new Product2();
        kitItemProd3.Name = 'Test';
        kitItemProd3.Brand_Name__c = 'Skil';
        kitItemProd3.RecordTypeId = proRecordType[0].Id;
        kitItemProd3.ProductCode = 'BY519901T';
        kitItemProd3.Country_of_Origin__c = 'United States';
        kitItemProd3.IsActive = true;
        kitItemProd3.Source__c = 'EBS';
        prodList.add(kitItemProd3);
        insert prodList;
        List<Kit_Item__c> kitItemList = new List<Kit_Item__c>();
        Kit_Item__c kitItem1 = new Kit_Item__c();
        kitItem1.Kit__c = masterProd.Id;
        kitItem1.Product__c = kitItemProd1.Id;
        kitItem1.RecordTypeId = rtd.Id;
        kitItem1.Source__c = 'EBS';
        kitItem1.Sequence__c = 'test';
        kitItem1.Repairable__c = true;
        kitItemList.add(kitItem1);
        Kit_Item__c kitItem2 = new Kit_Item__c();
        kitItem2.Kit__c = masterProd.Id;
        kitItem2.Product__c = kitItemProd2.Id;
        kitItem2.RecordTypeId = rtd.Id;
        kitItem2.Source__c = 'EBS';
        kitItem2.Repairable__c = true;
        kitItemList.add(kitItem2);
        Kit_Item__c kitItem3 = new Kit_Item__c();
        kitItem3.Kit__c = masterProd.Id;
        kitItem3.Product__c = kitItemProd3.Id;
        kitItem3.RecordTypeId = rtd.Id;
        kitItem3.Source__c = 'EBS';
        kitItem3.Sequence__c = 'test';
        kitItem3.Repairable__c = true;
        kitItemList.add(kitItem3);
        insert kitItemList;
        Warranty__c warrantyInfo = new Warranty__c();
        warrantyInfo.AccountCustomer__c = acc.Id;
        warrantyInfo.Master_Product__c = masterProd.Id;
        warrantyInfo.Brand_Name__c = 'Skil';
        warrantyInfo.Product_Use_Type2__c = 'Professional/Commercial';
        warrantyInfo.Lost_Receipt__c = true;
        insert warrantyInfo;
        List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>();
        Warranty_Item__c wItem1 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem1.Product__c = kitItemProd1.Id;
        wItem1.Serial_Number__c = 'NLM01190500001';
        warrantyItemList.add(wItem1);
        Warranty_Item__c wItem2 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem2.Product__c = kitItemProd1.Id;
        wItem2.Serial_Number__c = 'NLM01190500002';
        warrantyItemList.add(wItem2);
        Warranty_Item__c wItem3 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem3.Product__c = kitItemProd1.Id;
        wItem3.Serial_Number__c = 'NLM01190500003';
        warrantyItemList.add(wItem3);
        insert warrantyItemList;
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct(masterProd.Id, warrantyInfo.Id);
        //CCM_WarrantyController.getProductIfHaveSurvey(kitItemProd2.Id);
        try {
            //CCM_WarrantyController.getProductIfHaveSurvey('');
        } catch (Exception e) {
        }
        Test.stopTest();
    }
    @IsTest
    //Add by Abby on 4/4/2020
    static void testChangeMasterProduct5() {
        List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Kit'];
        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Product'];
        RecordType rtd = [SELECT Id FROM RecordType WHERE Name = 'Products and Parts' AND SobjectType = 'Kit_item__c' LIMIT 1];
        Account acc = TestDataFactory.createAccount();
        acc.Product_Type__c = 'Skil';
        acc.Phone = '********';
        acc.PersonEmail = '<EMAIL>';
        insert acc;
        List<Product2> prodList = new List<Product2>();
        Product2 masterProd = new Product2();
        masterProd.Name = 'Test';
        masterProd.Brand_Name__c = 'Skil';
        masterProd.RecordTypeId = kitRecordType[0].Id;
        masterProd.ProductCode = 'CB738401';
        masterProd.Country_of_Origin__c = 'United States';
        masterProd.IsActive = true;
        masterProd.Source__c = 'EBS';
        prodList.add(masterProd);
        Product2 kitItemProd1 = new Product2();
        kitItemProd1.Name = 'Test';
        kitItemProd1.Brand_Name__c = 'Skil';
        kitItemProd1.RecordTypeId = proRecordType[0].Id;
        kitItemProd1.ProductCode = 'BY519901';
        kitItemProd1.Country_of_Origin__c = 'United States';
        kitItemProd1.IsActive = true;
        kitItemProd1.Source__c = 'EBS';
        prodList.add(kitItemProd1);
        Product2 kitItemProd2 = new Product2();
        kitItemProd2.Name = 'Test';
        kitItemProd2.Brand_Name__c = 'Skil';
        kitItemProd2.RecordTypeId = proRecordType[0].Id;
        kitItemProd2.ProductCode = 'SC536501';
        kitItemProd2.Country_of_Origin__c = 'United States';
        kitItemProd2.IsActive = true;
        kitItemProd2.Source__c = 'EBS';
        prodList.add(kitItemProd2);
        Product2 kitItemProd3 = new Product2();
        kitItemProd3.Name = 'Test';
        kitItemProd3.Brand_Name__c = 'Skil';
        kitItemProd3.RecordTypeId = proRecordType[0].Id;
        kitItemProd3.ProductCode = 'BY519901T';
        kitItemProd3.Country_of_Origin__c = 'United States';
        kitItemProd3.IsActive = true;
        kitItemProd3.Source__c = 'EBS';
        prodList.add(kitItemProd3);
        insert prodList;
        List<Kit_Item__c> kitItemList = new List<Kit_Item__c>();
        Kit_Item__c kitItem1 = new Kit_Item__c();
        kitItem1.Kit__c = masterProd.Id;
        kitItem1.Product__c = kitItemProd1.Id;
        kitItem1.RecordTypeId = rtd.Id;
        kitItem1.Source__c = 'EBS';
        kitItem1.Sequence__c = 'test';
        kitItem1.Repairable__c = true;
        kitItemList.add(kitItem1);
        Kit_Item__c kitItem2 = new Kit_Item__c();
        kitItem2.Kit__c = masterProd.Id;
        kitItem2.Product__c = kitItemProd2.Id;
        kitItem2.RecordTypeId = rtd.Id;
        kitItem2.Source__c = 'EBS';
        kitItem2.Repairable__c = true;
        kitItemList.add(kitItem2);
        Kit_Item__c kitItem3 = new Kit_Item__c();
        kitItem3.Kit__c = masterProd.Id;
        kitItem3.Product__c = kitItemProd3.Id;
        kitItem3.RecordTypeId = rtd.Id;
        kitItem3.Source__c = 'EBS';
        kitItem3.Sequence__c = 'test';
        kitItem3.Repairable__c = true;
        kitItemList.add(kitItem3);
        insert kitItemList;
        Warranty__c warrantyInfo = new Warranty__c();
        warrantyInfo.AccountCustomer__c = acc.Id;
        warrantyInfo.Master_Product__c = masterProd.Id;
        warrantyInfo.Brand_Name__c = 'Skil';
        warrantyInfo.Product_Use_Type2__c = 'Professional/Commercial';
        warrantyInfo.Lost_Receipt__c = true;
        insert warrantyInfo;
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct(masterProd.Id, warrantyInfo.Id);
        Test.stopTest();
    }
    @IsTest
    static void testInit() {
        Account acc = [SELECT Id, Product_Type__c FROM Account LIMIT 1];
        Warranty__c w = [SELECT Id FROM Warranty__c LIMIT 1];
        Case cs = [SELECT Id FROM Case LIMIT 1];
        List<Warranty_Item__c> items = new List<Warranty_Item__c>{ TestDataFactory.createWarrantyItem(w.Id), TestDataFactory.createWarrantyItem(w.Id) };
        insert items;
        Test.startTest();
        CCM_WarrantyController.init(acc.Id, '', '');
        CCM_WarrantyController.init('', cs.Id, '');
        CCM_WarrantyController.init('', '', w.Id);
        CCM_WarrantyController.isCustomerBrand(acc.Id, acc.Product_Type__c);
        Test.stopTest();
    }
    @IsTest
    static void testChangeMasterProduct() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty__c w = [SELECT Id FROM Warranty__c LIMIT 1];
        Warranty__c w1 = [SELECT Id FROM Warranty__c WHERE Brand_Name__c = 'SkilSaw' LIMIT 1];
        CCM_WarrantyController.changeMasterProduct(p.Id, w.Id);
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct('', w.Id);
        CCM_WarrantyController.changeMasterProduct(p.Id, w1.Id);
        Test.stopTest();
    }
    @IsTest
    static void testChangeMasterProduct2() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty__c w = [SELECT Id, Brand_Name__c FROM Warranty__c LIMIT 1];
        w.Brand_Name__c = 'SkilSaw';
        update w;
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct(p.Id, w.Id);
        Test.stopTest();
    }
    @IsTest
    static void testChangeMasterProduct3() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty__c w = [SELECT Id, Brand_Name__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        w.Brand_Name__c = 'EGO';
        update w;
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct(p.Id, w.Id);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Product_Code__c = '*****************';
        update wi;
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'EGO', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator3() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = '11';
        update wi;
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'EGO', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator4() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = 'RLM201902000001X';
        wi.Product_Code__c = '*****************';
        update wi;
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'EGO', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator1() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'Skil', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator2() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'SkilSaw', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testSetWarrantyItemExpirationDateForSkilAndSkilSaw() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Brand_Name__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        Map<String, Decimal> product_warrantyYear_map = new Map<String, Decimal>();
        CCM_WarrantyController.setWarrantyItemExpirationDateForSkilAndSkilSaw(w, wi, product_warrantyYear_map);
        Test.stopTest();
    }
    @IsTest
    static void testSetWarrantyItemExpirationDateForSkilAndSkilSaw1() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Brand_Name__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        w.Product_Use_Type2__c = 'Residential';
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c, Product__c, Product_Model__c, Product_Type__c FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        Map<String, Decimal> product_warrantyYear_map = new Map<String, Decimal>();
        CCM_WarrantyController.setWarrantyItemExpirationDateForSkilAndSkilSaw(w, wi, product_warrantyYear_map);
        CCM_WarrantyController.setWarrantyItemExpirationDateForEGO(w, wi);
        Test.stopTest();
    }
    @IsTest
    static void testSave() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Case ca = [SELECT Id FROM Case LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('*********', pplist);
        CCM_WarrantyController.save(JSON.serialize(w), JSON.serialize(wiw), ca.Id);
        Test.stopTest();
    }
    @IsTest
    static void testSave1() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = 'N130800001';
        update wi;
        Case ca = [SELECT Id FROM Case LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('*********', pplist);
        CCM_WarrantyController.save(JSON.serialize(w), JSON.serialize(wiw), ca.Id);
        Test.stopTest();
    }
    @IsTest
    static void testSave2() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = 'RNLM041806 00001X';
        update wi;
        Case ca = [SELECT Id FROM Case LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('*********', pplist);
        CCM_WarrantyController.save(JSON.serialize(w), JSON.serialize(wiw), ca.Id);
        Test.stopTest();
    }
    @IsTest
    static void testChangeExpirationDate() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('*********', pplist);
        CCM_WarrantyController.changeExpirationDate(w, JSON.serialize(wiw));
        Test.stopTest();
    }
    @IsTest
    static void testSaveSurvey() {
        Test.startTest();
        Chervon_Survey__c objSurvey = new Chervon_Survey__c(Name = 'test');
        insert objSurvey;
        List<Chervon_Survey_Question__c> lstQuestion = new List<Chervon_Survey_Question__c>{
            new Chervon_Survey_Question__c(Chervon_Survey__c = objSurvey.Id, Question__c = 'test1', Type__c = 'Free Text', Required__c = true),
            new Chervon_Survey_Question__c(Chervon_Survey__c = objSurvey.Id, Question__c = 'test2', Type__c = 'Multi Select', Required__c = true)
        };
        insert lstQuestion;
        List<Chervon_Survey_Question_Choice__c> lstChoice = new List<Chervon_Survey_Question_Choice__c>{
            new Chervon_Survey_Question_Choice__c(Chervon_Survey_Question__c = lstQuestion[0].Id, Required__c = true, Choice__c = 'test1'),
            new Chervon_Survey_Question_Choice__c(Chervon_Survey_Question__c = lstQuestion[1].Id, Required__c = true, Choice__c = 'test2')
        };
        insert lstChoice;
        Account objAccount = new Account(Name = 'test');
        objAccount.TaxID__c = 'testTax';
        insert objAccount;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objAccount.Id);
        insert objWarranty;
        CCM_WarrantyController.ChoiceWrapper objChoiceWrapper1 = new CCM_WarrantyController.ChoiceWrapper();
        objChoiceWrapper1.id = lstChoice[0].Id;
        objChoiceWrapper1.checked = true;
        objChoiceWrapper1.answerText = 'test';
        CCM_WarrantyController.ChoiceWrapper objChoiceWrapper2 = new CCM_WarrantyController.ChoiceWrapper();
        objChoiceWrapper2.id = lstChoice[1].Id;
        objChoiceWrapper2.checked = true;
        objChoiceWrapper2.answerText = 'test';
        CCM_WarrantyController.QuestionChoiceWrapper objQuestionChoiceWrapper1 = new CCM_WarrantyController.QuestionChoiceWrapper();
        objQuestionChoiceWrapper1.id = lstQuestion[0].Id;
        objQuestionChoiceWrapper1.type = 'Free Text';
        objQuestionChoiceWrapper1.choices = new List<CCM_WarrantyController.ChoiceWrapper>{ objChoiceWrapper1 };
        CCM_WarrantyController.QuestionChoiceWrapper objQuestionChoiceWrapper2 = new CCM_WarrantyController.QuestionChoiceWrapper();
        objQuestionChoiceWrapper2.id = lstQuestion[0].Id;
        objQuestionChoiceWrapper2.type = 'Multi Select';
        objQuestionChoiceWrapper2.choices = new List<CCM_WarrantyController.ChoiceWrapper>{ objChoiceWrapper2 };
        List<CCM_WarrantyController.QuestionChoiceWrapper> lstQuestionChoiceWrapper = new List<CCM_WarrantyController.QuestionChoiceWrapper>{
            objQuestionChoiceWrapper1,
            objQuestionChoiceWrapper2
        };
        try {
            CCM_WarrantyController.saveSurvey(objSurvey.Id, objAccount.Id, objWarranty.Id, JSON.serialize(lstQuestionChoiceWrapper));
        } catch (Exception objE) {
            System.assertEquals(objE.getMessage(), 'test');
        }
        Test.stopTest();
    }
    @IsTest
    static void testGetProductIfHaveSurvey() { 
        Test.startTest();
        Chervon_Survey__c objSurvey = new Chervon_Survey__c(Name = 'test');
        insert objSurvey;
        Product2 objProduct = new Product2(Name = 'test', Chervon_Survey__c = objSurvey.Id);
        insert objProduct;
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CCM_WarrantyController.getProductIfHaveSurvey(objProduct.Id);
        Test.stopTest();
    }
    @IsTest
    static void testFindProjectsAccordingSerialNum() {
        Product2 p2 = new Product2();
        p2.Name = 'testProduct';
        p2.ProductCode = '1111';
        p2.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('Product2', 'Product');
        insert p2;
        Project__c pro = new Project__c();
        pro.Product__c = p2.Id;
        pro.Email_Template_Developer_Name__c = 'test';
        pro.Send_Email__c = false;
        pro.Deadline__c = Date.today() + 1;
        pro.Brand_Name__c = 'Skil';
        insert pro;
        Project_SN__c psn = new Project_SN__c();
        psn.Project__c = pro.Id;
        psn.Star_SN__c = 'psn start';
        psn.End_SN__c = 'psn end';
        insert psn;
        Test.startTest();
        CCM_WarrantyController.findProjectsAccordingSerialNum('*********', '1111', 'Skil');
        Test.stopTest();
    }
    @IsTest
    static void testGetAccountSiteOriginOutsideEuropeStatus() {
        Test.startTest();
        List<Account> lstAccount = new List<Account>{
            new Account(Name = 'Test1', Site_Origin__c = 'United State', TaxID__c = 'testTax'),
            new Account(Name = 'Test1', Site_Origin__c = 'United Kingdom', TaxID__c = 'testTax')
        };
        insert lstAccount;
        System.assert(CCM_WarrantyController.getAccountSiteOriginOutsideEuropeStatus(lstAccount[0].Id) == true);
        System.assert(CCM_WarrantyController.getAccountSiteOriginOutsideEuropeStatus(lstAccount[1].Id) == false);
        Test.stopTest();
    }
    @IsTest
    static void test3DigitsHammerheadSN() {
        Test.startTest();
        Account objEndUserAccount = new Account(LastName = 'test', RecordTypeId = CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID);
        insert objEndUserAccount;
        Product2 objProduct = new Product2(Name = 'test', Brand_Name__c = 'Hammerhead', IsActive = true);
        insert objProduct;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objEndUserAccount.Id);
        insert objWarranty;
        Warranty_Item__c objWarrantyItem = new Warranty_Item__c(Warranty__c = objWarranty.Id, Serial_Number__c = '112', Product_Code__c = '*****************');
        String strResult = CCM_WarrantyController.checkSNAndUpdateIndicator(
            objProduct.Id,
            String.valueOf(Date.today()),
            'Professional/Commercial',
            'ACE',
            'Hammerhead',
            JSON.serialize(
                new CCM_WarrantyController.WarrantyItemWrapper('1121', new List<CCM_WarrantyController.Product3>{ new CCM_WarrantyController.Product3(objWarrantyItem) })
            ),
            false
        );
        Test.stopTest();
        System.assertEquals(true, strResult != null && strResult.startsWith('{"proList":[{"warrantyItem":{"attributes":{"type":"Warranty_Item__c"}'));
    }
    @IsTest
    static void testGetEuropeanProduct() {
        Test.startTest();
        Account objEndCustomer = new Account(LastName = 'test', RecordTypeId = CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID);
        insert objEndCustomer;
        Product2 objProduct = new Product2(
            Name = 'test',
            IsActive = true,
            Country_of_Origin__c = Label.CCM_Product_Country_Origin_European_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED)[0]
        );
        insert objProduct;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objEndCustomer.Id);
        insert objWarranty;
        Warranty_Item__c objWarrantyItem = new Warranty_Item__c(Warranty__c = objWarranty.Id, Product__c = objProduct.Id);
        insert objWarrantyItem;
        Set<Id> setEuProduct = CCM_WarrantyController.getEuropeanProduct(new List<CCM_WarrantyController.Product3>{ new CCM_WarrantyController.Product3(objWarrantyItem) });
        Test.stopTest();
        System.assertEquals(1, setEuProduct.size());
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator4Flex() {
        Test.startTest();
        Account objEndCustomer = new Account(LastName = 'test', RecordTypeId = CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID);
        insert objEndCustomer;
        Product2 objProduct = new Product2(Name = 'test', IsActive = true, Brand_Name__c = 'Flex');
        insert objProduct;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objEndCustomer.Id);
        insert objWarranty;
        Warranty_Item__c objWarrantyItem = new Warranty_Item__c(Warranty__c = objWarranty.Id, Product__c = objProduct.Id, Serial_Number__c = '*************', Product_Code__c = '*****************');
        insert objWarrantyItem;
        CCM_WarrantyController.checkSNAndUpdateIndicator(
            objProduct.Id,
            String.valueOf(Date.today()),
            'Professional/Commercial',
            'ACE',
            'FLEX',
            JSON.serialize(
                new CCM_WarrantyController.WarrantyItemWrapper('test', new List<CCM_WarrantyController.Product3>{ new CCM_WarrantyController.Product3(objWarrantyItem) })
            ),
            false
        );
        Test.stopTest();
    }

    // 新增测试方法 - 测试setStoreReturnExchangePolicy方法
    @IsTest
    static void testSetStoreReturnExchangePolicy() {
        Test.startTest();
        Warranty__c w = [SELECT Id, Purchase_Date__c, Place_of_Purchase_picklist__c FROM Warranty__c LIMIT 1];
        w.Purchase_Date__c = Date.today().addDays(-30);
        w.Place_of_Purchase_picklist__c = 'Home Depot';
        CCM_WarrantyController.setStoreReturnExchangePolicy(w);

        w.Place_of_Purchase_picklist__c = 'Amazon LLC';
        CCM_WarrantyController.setStoreReturnExchangePolicy(w);

        w.Place_of_Purchase_picklist__c = 'Ace Hardware';
        CCM_WarrantyController.setStoreReturnExchangePolicy(w);

        w.Place_of_Purchase_picklist__c = 'Other';
        CCM_WarrantyController.setStoreReturnExchangePolicy(w);
        Test.stopTest();
    }

    // 新增测试方法 - 测试setWarrantyItemIndicator方法
    @IsTest
    static void testSetWarrantyItemIndicator() {
        Test.startTest();
        Warranty__c w = [SELECT Id, Purchase_Date__c, Receipt_received_and_verified__c, Place_of_Purchase_picklist__c FROM Warranty__c LIMIT 1];
        w.Receipt_received_and_verified__c = true;
        w.Place_of_Purchase_picklist__c = 'Home Depot';
        List<Warranty_Item__c> items = [SELECT Id, Serial_Number__c, Expiration_Date_New__c FROM Warranty_Item__c LIMIT 2];

        // 测试有效保修情况
        for(Warranty_Item__c item : items) {
            item.Serial_Number__c = 'TEST123456';
            item.Expiration_Date_New__c = Date.today().addDays(30);
        }
        update items;

        CCM_WarrantyController.setWarrantyItemIndicator(w, items);

        // 测试过期保修情况
        for(Warranty_Item__c item : items) {
            item.Expiration_Date_New__c = Date.today().addDays(-30);
        }
        update items;

        CCM_WarrantyController.setWarrantyItemIndicator(w, items);

        // 测试未知购买地情况
        w.Place_of_Purchase_picklist__c = 'Unknown';
        CCM_WarrantyController.setWarrantyItemIndicator(w, items);
        Test.stopTest();
    }

    // 新增测试方法 - 测试countCost方法
    @IsTest
    static void testCountCost() {
        Test.startTest();
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty__c w = [SELECT Id FROM Warranty__c LIMIT 1];

        // 创建价格手册条目
        PricebookEntry pbe = new PricebookEntry();
        pbe.Pricebook2Id = Test.getStandardPricebookId();
        pbe.Product2Id = p.Id;
        pbe.UnitPrice = 100;
        insert pbe;

        CCM_WarrantyController.warrService = new WarrantyService();
        CCM_WarrantyController.warrService.warranty = w;
        CCM_WarrantyController.warrService.masterProPBE = pbe;

        CCM_WarrantyController.countCost();
        Test.stopTest();
    }

    // 新增测试方法 - 测试changeExpirationDate方法的重载版本
    @IsTest
    static void testChangeExpirationDateOverload() {
        Test.startTest();
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        List<Warranty_Item__c> items = [SELECT Id FROM Warranty_Item__c LIMIT 2];

        // 测试changeExpirationDate方法的List<Warranty_Item__c>重载版本
        CCM_WarrantyController.changeExpirationDate(w, items);

        // 测试空值情况
        w.Purchase_Date__c = null;
        CCM_WarrantyController.changeExpirationDate(w, items);

        // 测试空列表
        CCM_WarrantyController.changeExpirationDate(w, new List<Warranty_Item__c>());
        Test.stopTest();
    }

    // 新增测试方法 - 测试saveWarrantyItem方法的异常处理
    @IsTest
    static void testSaveWarrantyItemException() {
        Test.startTest();
        Warranty__c w = [SELECT Id FROM Warranty__c LIMIT 1];

        // 创建无效的warranty item数据来触发异常
        List<Warranty_Item__c> invalidItems = new List<Warranty_Item__c>();
        Warranty_Item__c invalidItem = new Warranty_Item__c();
        invalidItem.Warranty__c = w.Id;
        // 故意不设置必需字段来触发异常
        invalidItems.add(invalidItem);

        try {
            CCM_WarrantyController.saveWarrantyItem(w, invalidItems);
        } catch (Exception e) {
            System.assert(e != null, '应该捕获到异常');
        }
        Test.stopTest();
    }

    // 新增测试方法 - 测试不同品牌的序列号验证
    @IsTest
    static void testCheckSNForDifferentBrands() {
        Test.startTest();
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];

        // 测试EGO品牌
        wi.Serial_Number__c = 'EGO123456789';
        update wi;
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('test', pplist);

        String result1 = CCM_WarrantyController.checkSNAndUpdateIndicator(
            String.valueOf(p.Id),
            String.valueOf(Date.today()),
            'Professional/Commercial',
            'Home Depot',
            'EGO',
            JSON.serialize(wiw),
            false
        );
        System.assert(result1 != null, '结果不应为空');

        // 测试Hammerhead品牌
        String result2 = CCM_WarrantyController.checkSNAndUpdateIndicator(
            String.valueOf(p.Id),
            String.valueOf(Date.today()),
            'Professional/Commercial',
            'Home Depot',
            'Hammerhead',
            JSON.serialize(wiw),
            false
        );
        System.assert(result2 != null, '结果不应为空');
        Test.stopTest();
    }

    // 新增测试方法 - 测试错误处理和边界条件
    @IsTest
    static void testErrorHandlingAndBoundaryConditions() {
        Test.startTest();

        // 测试checkSNAndUpdateIndicator方法的错误处理
        String errorResult = CCM_WarrantyController.checkSNAndUpdateIndicator(
            null,
            String.valueOf(Date.today()),
            'Professional/Commercial',
            'Home Depot',
            'EGO',
            '{}',
            false
        );
        System.assertEquals('Master Product ID Can not be null', errorResult, '应该返回错误信息');

        // 测试空字符串产品ID
        String errorResult2 = CCM_WarrantyController.checkSNAndUpdateIndicator(
            '',
            String.valueOf(Date.today()),
            'Professional/Commercial',
            'Home Depot',
            'EGO',
            '{}',
            false
        );
        System.assertEquals('Master Product ID Can not be null', errorResult2, '应该返回错误信息');

        Test.stopTest();
    }

    // 新增测试方法 - 测试save方法的更多场景
    @IsTest
    static void testSaveMethodMoreScenarios() {
        Test.startTest();
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c, Place_of_Purchase_picklist__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Case ca = [SELECT Id FROM Case LIMIT 1];

        // 测试空的warranty item列表
        CCM_WarrantyController.WarrantyItemWrapper emptyWiw = new CCM_WarrantyController.WarrantyItemWrapper('test', new List<CCM_WarrantyController.Product3>());
        String result1 = CCM_WarrantyController.save(JSON.serialize(w), JSON.serialize(emptyWiw), ca.Id);
        System.assertEquals('Warranty Item can not be null', result1, '应该返回错误信息');

        // 测试缺少必需字段的情况
        w.Place_of_Purchase_picklist__c = null;
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('test', pplist);

        String result2 = CCM_WarrantyController.save(JSON.serialize(w), JSON.serialize(wiw), ca.Id);
        System.assert(result2.contains('Purchase Date / Place of Purchase can not be null'), '应该返回必需字段错误');

        Test.stopTest();
    }

    // 新增测试方法 - 测试不同的序列号格式
    @IsTest
    static void testDifferentSerialNumberFormats() {
        Test.startTest();
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];

        // 测试各种序列号格式
        List<String> serialNumbers = new List<String>{
            'A12345678901',
            'B98765432109',
            'C11111111111',
            'INVALID_SN',
            '123456789012',
            ''
        };

        for(String sn : serialNumbers) {
            wi.Serial_Number__c = sn;
            update wi;

            CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
            List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
            pplist.add(pp);
            CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('test', pplist);

            String result = CCM_WarrantyController.checkSNAndUpdateIndicator(
                String.valueOf(p.Id),
                String.valueOf(Date.today()),
                'Professional/Commercial',
                'Home Depot',
                'EGO',
                JSON.serialize(wiw),
                false
            );
            System.assert(result != null, '结果不应为空，序列号: ' + sn);
        }

        Test.stopTest();
    }

    // 新增测试方法 - 测试不同的购买地和使用类型组合
    @IsTest
    static void testDifferentPurchasePlaceAndUseType() {
        Test.startTest();
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = 'TEST123456';
        update wi;

        List<String> purchasePlaces = new List<String>{'Home Depot', 'Amazon LLC', 'Ace Hardware', 'Other', 'Unknown'};
        List<String> useTypes = new List<String>{'Professional/Commercial', 'Residential'};
        List<String> brands = new List<String>{'EGO', 'Skil', 'SkilSaw', 'Hammerhead', 'Flex'};

        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('test', pplist);

        for(String place : purchasePlaces) {
            for(String useType : useTypes) {
                for(String brand : brands) {
                    String result = CCM_WarrantyController.checkSNAndUpdateIndicator(
                        String.valueOf(p.Id),
                        String.valueOf(Date.today()),
                        useType,
                        place,
                        brand,
                        JSON.serialize(wiw),
                        false
                    );
                    System.assert(result != null, '结果不应为空，组合: ' + place + '-' + useType + '-' + brand);
                }
            }
        }

        Test.stopTest();
    }

    // 新增测试方法 - 测试getPurchasePlaceIsAuthorized方法
    @IsTest
    static void testGetPurchasePlaceIsAuthorized() {
        Test.startTest();
        Warranty__c w = [SELECT Id, Place_of_Purchase_picklist__c FROM Warranty__c LIMIT 1];

        // 测试授权购买地
        w.Place_of_Purchase_picklist__c = 'Home Depot';
        String result1 = CCM_WarrantyController.getPurchasePlaceIsAuthorized(w);
        System.assertEquals('Authorized', result1, 'Home Depot应该是授权购买地');

        // 测试未授权购买地
        w.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        String result2 = CCM_WarrantyController.getPurchasePlaceIsAuthorized(w);
        System.assertEquals('Unauthorized', result2, 'Unauthorized Dealer应该是未授权购买地');

        // 测试未知购买地
        w.Place_of_Purchase_picklist__c = 'Unknown';
        String result3 = CCM_WarrantyController.getPurchasePlaceIsAuthorized(w);
        System.assertEquals('Unknown', result3, 'Unknown应该是未知购买地');

        // 测试其他购买地
        w.Place_of_Purchase_picklist__c = 'Other Store';
        String result4 = CCM_WarrantyController.getPurchasePlaceIsAuthorized(w);
        System.assertEquals('Unknown', result4, '其他购买地应该返回Unknown');

        Test.stopTest();
    }

    // 新增测试方法 - 测试更多的setWarrantyItemExpirationDate场景
    @IsTest
    static void testSetWarrantyItemExpirationDateMoreScenarios() {
        Test.startTest();
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Brand_Name__c FROM Warranty__c LIMIT 1];
        List<Warranty_Item__c> items = [SELECT Id, Product_Type__c, Product_Model__c FROM Warranty_Item__c LIMIT 2];

        // 设置不同的产品类型和型号
        items[0].Product_Type__c = 'Battery';
        items[0].Product_Model__c = '5';
        items[1].Product_Type__c = 'Charger';
        items[1].Product_Model__c = '3';
        update items;

        // 测试EGO品牌
        w.Brand_Name__c = 'EGO';
        w.Product_Use_Type2__c = 'Residential';
        CCM_WarrantyController.setWarrantyItemExpirationDate(w, items);

        // 测试Skil品牌 - Professional/Commercial
        w.Brand_Name__c = 'Skil';
        w.Product_Use_Type2__c = 'Professional/Commercial';
        Map<String, Decimal> warrantyYearMap = new Map<String, Decimal>();
        warrantyYearMap.put('Product', 1.0);
        warrantyYearMap.put('Battery', 2.0);
        warrantyYearMap.put('Charger', 1.0);

        for(Warranty_Item__c item : items) {
            CCM_WarrantyController.setWarrantyItemExpirationDateForSkilAndSkilSaw(w, item, warrantyYearMap);
        }

        // 测试SkilSaw品牌
        w.Brand_Name__c = 'SkilSaw';
        for(Warranty_Item__c item : items) {
            CCM_WarrantyController.setWarrantyItemExpirationDateForSkilAndSkilSaw(w, item, warrantyYearMap);
        }

        // 测试空购买日期
        w.Purchase_Date__c = null;
        for(Warranty_Item__c item : items) {
            CCM_WarrantyController.setWarrantyItemExpirationDateForSkilAndSkilSaw(w, item, warrantyYearMap);
        }

        Test.stopTest();
    }

    // 新增测试方法 - 测试Product3和WarrantyItemWrapper类的功能
    @IsTest
    static void testWrapperClasses() {
        Test.startTest();
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];

        // 测试Product3构造函数
        CCM_WarrantyController.Product3 product3 = new CCM_WarrantyController.Product3(wi);
        System.assert(product3.warrantyItem != null, 'warrantyItem应该被设置');
        System.assertEquals(true, product3.isFormatCorrect, '默认格式应该为true');
        System.assertEquals(false, product3.inRecallProject, '默认召回状态应该为false');

        // 测试WarrantyItemWrapper构造函数
        List<CCM_WarrantyController.Product3> productList = new List<CCM_WarrantyController.Product3>{product3};
        CCM_WarrantyController.WarrantyItemWrapper wrapper = new CCM_WarrantyController.WarrantyItemWrapper('testMaster', productList);
        System.assertEquals('testMaster', wrapper.productCode, 'productCode应该被正确设置');
        System.assertEquals(1, wrapper.proList.size(), 'proList大小应该为1');

        Test.stopTest();
    }

    // 新增测试方法 - 测试Survey相关功能的边界条件
    @IsTest
    static void testSurveyBoundaryConditions() {
        Test.startTest();

        // 测试getProductIfHaveSurvey方法的空参数
        try {
            Product2 result = CCM_WarrantyController.getProductIfHaveSurvey('');
            System.assert(result != null, '应该处理空参数');
        } catch (Exception e) {
            System.assert(true, '应该捕获异常');
        }

        // 测试getProductIfHaveSurvey方法的无效ID
        try {
            Product2 result = CCM_WarrantyController.getProductIfHaveSurvey('invalid_id');
            System.assert(result != null, '应该处理无效ID');
        } catch (Exception e) {
            System.assert(true, '应该捕获异常');
        }

        Test.stopTest();
    }

    // 新增测试方法 - 测试更多的保修项指示器场景
    @IsTest
    static void testWarrantyItemIndicatorMoreScenarios() {
        Test.startTest();
        Warranty__c w = [SELECT Id, Receipt_received_and_verified__c, Lost_Receipt__c, Pending__c, Place_of_Purchase_picklist__c FROM Warranty__c LIMIT 1];
        List<Warranty_Item__c> items = [SELECT Id, Serial_Number__c, Expiration_Date_New__c FROM Warranty_Item__c LIMIT 2];

        // 测试Lost_Receipt为true的情况
        w.Lost_Receipt__c = true;
        w.Receipt_received_and_verified__c = false;
        w.Pending__c = false;
        w.Place_of_Purchase_picklist__c = 'Home Depot';

        for(Warranty_Item__c item : items) {
            item.Serial_Number__c = 'TEST123456';
            item.Expiration_Date_New__c = Date.today().addDays(30);
        }
        update items;

        CCM_WarrantyController.setWarrantyItemIndicator(w, items);

        // 测试Pending为true的情况
        w.Lost_Receipt__c = false;
        w.Pending__c = true;
        CCM_WarrantyController.setWarrantyItemIndicator(w, items);

        // 测试无序列号且未验证发票的情况
        for(Warranty_Item__c item : items) {
            item.Serial_Number__c = '';
        }
        update items;
        w.Receipt_received_and_verified__c = false;
        CCM_WarrantyController.setWarrantyItemIndicator(w, items);

        Test.stopTest();
    }

    // 新增测试方法 - 测试findProjectsAccordingSerialNum的更多场景
    @IsTest
    static void testFindProjectsAccordingSerialNumMoreScenarios() {
        Test.startTest();

        // 测试不同的序列号和产品代码组合
        List<String> serialNumbers = new List<String>{'SN001', 'SN002', 'INVALID_SN'};
        List<String> productCodes = new List<String>{'PC001', 'PC002', 'INVALID_PC'};
        List<String> brands = new List<String>{'EGO', 'Skil', 'SkilSaw', 'Unknown'};

        for(String sn : serialNumbers) {
            for(String pc : productCodes) {
                for(String brand : brands) {
                    List<Project_SN__c> result =
                        CCM_WarrantyController.findProjectsAccordingSerialNum(sn, pc, brand);
                    System.assert(result != null, '结果不应为空');
                }
            }
        }

        Test.stopTest();
    }

    // 新增测试方法 - 测试isCustomerBrand方法的更多场景
    @IsTest
    static void testIsCustomerBrandMoreScenarios() {
        Test.startTest();
        Account acc = [SELECT Id FROM Account LIMIT 1];

        // 测试不同的品牌组合
        List<String> brands = new List<String>{'EGO', 'Skil', 'SkilSaw', 'Hammerhead', 'Flex', 'Unknown'};

        for(String brand : brands) {
            Boolean result = CCM_WarrantyController.isCustomerBrand(acc.Id, brand);
            System.assert(result != null, '结果不应为空，品牌: ' + brand);
        }

        // 测试空品牌
        Boolean result1 = CCM_WarrantyController.isCustomerBrand(acc.Id, '');
        System.assert(result1 != null, '空品牌应该有返回值');

        // 测试空账户ID
        Boolean result2 = CCM_WarrantyController.isCustomerBrand('', 'EGO');
        System.assert(result2 != null, '空账户ID应该有返回值');

        // 测试无效账户ID
        Boolean result3 = CCM_WarrantyController.isCustomerBrand('invalid_id', 'EGO');
        System.assert(result3 != null, '无效账户ID应该有返回值');

        Test.stopTest();
    }

    // 新增测试方法 - 测试更多的序列号验证场景
    @IsTest
    static void testSerialNumberValidationMoreScenarios() {
        Test.startTest();
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];

        // 测试特殊格式的序列号
        List<String> specialSerialNumbers = new List<String>{
            'FC123456789012',  // FC结尾的产品
            'TEST-FC',         // FC结尾的产品
            'ABC123456789',    // 普通序列号
            '1234567890123',   // 数字序列号
            'SPECIAL_SN_TEST', // 特殊字符序列号
            ''                 // 空序列号
        };

        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);

        for(String sn : specialSerialNumbers) {
            wi.Serial_Number__c = sn;
            update wi;

            pp = new CCM_WarrantyController.Product3(wi);
            pplist.clear();
            pplist.add(pp);
            CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('test', pplist);

            String result = CCM_WarrantyController.checkSNAndUpdateIndicator(
                String.valueOf(p.Id),
                String.valueOf(Date.today()),
                'Professional/Commercial',
                'Home Depot',
                'EGO',
                JSON.serialize(wiw),
                false
            );
            System.assert(result != null, '结果不应为空，序列号: ' + sn);

            // 测试编辑模式
            String resultEdit = CCM_WarrantyController.checkSNAndUpdateIndicator(
                String.valueOf(p.Id),
                String.valueOf(Date.today()),
                'Professional/Commercial',
                'Home Depot',
                'EGO',
                JSON.serialize(wiw),
                true
            );
            System.assert(resultEdit != null, '编辑模式结果不应为空，序列号: ' + sn);
        }

        Test.stopTest();
    }
}