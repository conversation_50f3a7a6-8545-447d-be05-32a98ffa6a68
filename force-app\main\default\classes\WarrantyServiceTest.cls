/**
 * @Author:          Zack
 * @Date:            2018-02-23
 * @Description:
 * @Test_Class:      WarrantyService
 * @Related_Class:
 * @Last_Modified_by: <PERSON>
 * @Last_Modified_time: 2018-08-13 14:34:54
 * @Modifiy_Purpose:    WarrantyService发生更改，相应更改测试类
 */
@isTest
private class WarrantyServiceTest {
	/**
	 * @Author:     Zack
	 * @Date:       2018-02-23
	 * @Return:     
	 * @Function:   测试WarrantyService的构造方法、setStoreReturnExchangePolicy、setWarrantyItemExpirationDate等方法
	 * @Last_Modified_by:  Zack
 	 * @Last_Modified_time:2018-07-12
 	 * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
	 */
    static testMethod void testMethodsOne() {
    	//========初始化数据========
    	//1、创建Account数据
    	Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        Account_SyncToShopify obj = new Account_SyncToShopify();
        Account_SyncToShopify.functionName.add('Account');
        CCM_SharingUtil.isSharingOnly = true;
    	insert acc;

    	//2、创建Product数据
    	Product2 pro = TestDataFactory.createProduct();

        //3、创建Price Book数据
        Default_PriceBook__c dp = new Default_PriceBook__c();
        dp.Place_of_Purchase__c = 'Home Depot';
        dp.Name = 'HomeDepot';
        insert dp;

        Default_PriceBook__c dp2 = new Default_PriceBook__c();
        dp2.Place_of_Purchase__c = 'Amazon LLC';
        dp2.Name = 'AmazonLLc';
        insert dp2;

        Default_PriceBook__c dp3 = new Default_PriceBook__c();
        dp3.Place_of_Purchase__c = 'Ferguson';
        dp3.Name = 'Ferguson';
        insert dp3;

        Default_PriceBook__c dp4 = new Default_PriceBook__c();
        dp4.Place_of_Purchase__c = 'Other';
        dp4.Name = 'Other';
        insert dp4;
        
        System_Configuration__c sc = new System_Configuration__c();
        sc.Name = 'Claim Auto Approval';
        sc.Is_Active__c = true;
        insert sc;


    	//4、创建Warranty数据
    	Warranty__c war = TestDataFactory.createWarranty();
    	war.AccountCustomer__c = acc.Id;
    	war.Store_return_exchange_policy__c = Date.today().addDays(60);
    	war.Purchase_Date__c = Date.today();
    	war.Master_Product__c = pro.Id;
    	insert war;

		Test.startTest();
    	//5、创建warranty item数据
    	Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
    	wi.Product_Model__c = '3';
    	wi.Warranty__c = war.Id;
    	// insert wi;
		Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
        wi2.Product_Type__c = 'Product';
        // insert wi2;
        Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
        // insert wi3;
        Warranty_Item__c wi4 = TestDataFactory.createWarrantyItem(war.Id);
        wi4.Product_Model__c = '3';
        wi4.Warranty__c = war.Id;
        // insert wi4;
        Warranty_Item__c wi5 = TestDataFactory.createWarrantyItem(war.Id);
        wi5.Warranty__c = war.Id;
        wi5.Product_Type__c = 'Product';
        // insert wi5;
        Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
        wi6.Warranty__c = war.Id;
        Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
        wi7.Warranty__c = war.Id;

    	List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
    	wiList.add(wi);
        wiList.add(wi2);
        wiList.add(wi3);
        wiList.add(wi4);
    	wiList.add(wi5);
		insert wiList;
    	//6、创建SystemConfiguration
    	TestDataFactory.createSystemConfig('v3');
			
        //========开始测试========
        //1、测试构造方法
        WarrantyService ws = new WarrantyService();

        //2、测试setStoreReturnExchangePolicy方法
        //  测试BrandName为skil的情况
        war.Brand_Name__c = 'Skil';
        /* war.Place_of_Purchase_picklist__c = 'Wal-mart';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();*/

        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //  测试BrandName为SkilSaw的情况
        war.Brand_Name__c = 'SkilSaw';
        war.Place_of_Purchase_picklist__c = 'Menards';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        /*war.Place_of_Purchase_picklist__c = 'Ferguson';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();*/

        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //  测试BrandName为未知的情况
        war.Brand_Name__c = 'other';
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //3、测试setWarrantyItemExpirationDate方法
        ws.warrantyItemList = wiList;
        //  测试Brand Name为Skil的情况
        war.Brand_Name__c = 'Skil';
        war.Purchase_Date__c = null;
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        war.Purchase_Date__c = System.today();
        war.Product_Use_Type2__c = 'Residential';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        war.Product_Use_Type2__c = 'Professional/Commercial';
        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        //  测试Brand Name为SkilSaw的情况
        war.Brand_Name__c = 'SkilSaw';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);

        //  测试Brand Name为未知的情况
        war.Brand_Name__c = 'Unknown';
        war.Purchase_Date__c = null;
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        Test.stopTest();
        war.Purchase_Date__c = System.today();
        war.Product_Use_Type2__c = 'Residential';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);

        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        
        ws.setStoreReturnExchangePolicy();
        Attachment att = new Attachment();
        att.Body = Blob.valueOf( 'Testing' );
        att.Name = 'test att';
        att.ParentId = war.Id;
        insert att;
        ws.receiptAttachmentID = att.Id;
        ws.relateWarrantyAndAttachment();
    
        Set<String> warrantyIdSet = new Set<String>();
        warrantyIdSet.add(war.Id);
        WarrantyService.getWarrantyNoItemsMapByIdSet(warrantyIdSet);
        
    }

    /**
     * @Author:     Zack
     * @Date:       2018-02-23
     * @Return:     
     * @Function:   测试WarrantyService的构造方法、countCost、
     *              saveWarrantyItem、uploadReceiptAttachment、relateWarrantyAndAttachment等方法
     * @Last_Modified_by:  Zack
     * @Last_Modified_time:2018-07-12
     * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
     */
    static testMethod void testMethodsOne2() {
        //========初始化数据========
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Price Book数据
        Default_PriceBook__c dp = new Default_PriceBook__c();
        dp.Place_of_Purchase__c = 'Home Depot';
        dp.Name = 'HomeDepot';
        insert dp;

        Default_PriceBook__c dp2 = new Default_PriceBook__c();
        dp2.Place_of_Purchase__c = 'Amazon LLC';
        dp2.Name = 'AmazonLLc';
        insert dp2;

        Default_PriceBook__c dp3 = new Default_PriceBook__c();
        dp3.Place_of_Purchase__c = 'Ferguson';
        dp3.Name = 'Ferguson';
        insert dp3;

        Default_PriceBook__c dp4 = new Default_PriceBook__c();
        dp4.Place_of_Purchase__c = 'Other';
        dp4.Name = 'Other';
        insert dp4;

        PricebookEntry pbe = new PricebookEntry();
        pbe.Pricebook2Id = Test.getStandardPricebookId();
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 66;
        insert pbe;


        //4、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Store_return_exchange_policy__c = Date.today().addDays(60);
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        //5、创建warranty item数据
        Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
        wi6.Warranty__c = war.Id;
        Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
        wi7.Warranty__c = war.Id;

        //6、创建SystemConfiguration
        TestDataFactory.createSystemConfig('v3');
		
        Test.startTest();
        //========开始测试========
        //1、测试构造方法
        WarrantyService ws = new WarrantyService();

        //4、测试countCost方法
        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Amazon LLC';
        ws.warranty = war;
        ws.countCost();

        //5、测试saveWarrantyItem方法
        List<Warranty_Item__c> wiList2 = new List<Warranty_Item__c>();
        wiList2.add(wi6);
        ws.warrantyItemList = wiList2;
        ws.saveWarrantyItem();

        wiList2 = new List<Warranty_Item__c>();
        wiList2.add(wi7);
        ws.warrantyItemList = wiList2;
        ws.masterProductOld = war.Master_Product__r.Id;
        ws.saveWarrantyItem();

        //6、测试uploadReceiptAttachment方法
        ws.uploadReceiptAttachment(Blob.valueOf('ssssssss'), 'jepg');

        //7、测试relateWarrantyAndAttachment方法
        //******
        //ws.relateWarrantyAndAttachment();
        Test.stopTest();

    }

    /**
     * @Author:     Zack
     * @Date:       2018-02-23
     * @Return:     
     * @Function:   测试WarrantyService的构造方法、countCost、
     *              saveWarrantyItem、uploadReceiptAttachment、relateWarrantyAndAttachment等方法
     * @Last_Modified_by:  Zack
     * @Last_Modified_time:2018-07-12
     * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
     */
    // static testMethod void testMethodsOne3() {
    //     //========初始化数据========
    //     //1、创建Account数据
    //     Account acc = TestDataFactory.createAccount();
    //     insert acc;

    //     //2、创建Product数据
    //     Product2 pro = TestDataFactory.createProduct();
    //     insert pro;

    //     //3、创建Price Book数据
    //     Default_PriceBook__c dp = new Default_PriceBook__c();
    //     dp.Place_of_Purchase__c = 'Home Depot';
    //     dp.Name = 'HomeDepot';
    //     insert dp;

    //     Default_PriceBook__c dp2 = new Default_PriceBook__c();
    //     dp2.Place_of_Purchase__c = 'Amazon LLC';
    //     dp2.Name = 'AmazonLLc';
    //     insert dp2;

    //     Default_PriceBook__c dp3 = new Default_PriceBook__c();
    //     dp3.Place_of_Purchase__c = 'Ferguson';
    //     dp3.Name = 'Ferguson';
    //     insert dp3;

    //     Default_PriceBook__c dp4 = new Default_PriceBook__c();
    //     dp4.Place_of_Purchase__c = 'Other';
    //     dp4.Name = 'Other';
    //     insert dp4;

    //     /*PricebookEntry pbe = new PricebookEntry();
    //     pbe.Pricebook2Id = dp.Id;
    //     pbe.Product2Id = pro.Id;
    //     pbe.UnitPrice = 66;
    //     insert pbe;

    //     PricebookEntry pbe2 = new PricebookEntry();
    //     pbe2.Pricebook2Id = dp2.Id;
    //     pbe2.Product2Id = pro.Id;
    //     pbe2.UnitPrice = 66;
    //     insert pbe2;

    //     PricebookEntry pbe3 = new PricebookEntry();
    //     pbe3.Pricebook2Id = dp3.Id;
    //     pbe3.Product2Id = pro.Id;
    //     pbe3.UnitPrice = 66;
    //     insert pbe3;*/

    //     //4、创建Warranty数据
    //     Warranty__c war = TestDataFactory.createWarranty();
    //     war.AccountCustomer__c = acc.Id;
    //     war.Store_return_exchange_policy__c = Date.today().addDays(60);
    //     war.Purchase_Date__c = Date.today();
    //     war.Master_Product__c = pro.Id;
    //     insert war;

    //     //5、创建warranty item数据
    //     Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
    //     wi.Product_Model__c = '3';
    //     wi.Warranty__c = war.Id;
    //     insert wi;
    //     Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi2.Product_Type__c = 'Product';
    //     insert wi2;
    //     Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
    //     insert wi3;
    //     Warranty_Item__c wi4 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi4.Product_Model__c = '3';
    //     wi4.Warranty__c = war.Id;
    //     insert wi4;
    //     Warranty_Item__c wi5 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi5.Warranty__c = war.Id;
    //     wi5.Product_Type__c = 'Product';
    //     insert wi5;
    //     Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi6.Warranty__c = war.Id;
    //     Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi7.Warranty__c = war.Id;

    //     List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
    //     wiList.add(wi);
    //     wiList.add(wi2);
    //     wiList.add(wi3);
    //     wiList.add(wi4);
    //     wiList.add(wi5);

    //     //6、创建SystemConfiguration
    //     TestDataFactory.createSystemConfig('v3');

    //     //========开始测试========
    //     //1、测试构造方法
    //     WarrantyService ws = new WarrantyService();

    //     //4、测试countCost方法
    //     war.Place_of_Purchase_picklist__c = 'Unknown';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Home Depot';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Amazon LLC';
    //     ws.warranty = war;
    //     ws.countCost();

    //     //5、测试saveWarrantyItem方法
    //     List<Warranty_Item__c> wiList2 = new List<Warranty_Item__c>();
    //     wiList2.add(wi6);
    //     ws.warrantyItemList = wiList2;
    //     ws.saveWarrantyItem();

    //     wiList2 = new List<Warranty_Item__c>();
    //     wiList2.add(wi7);
    //     ws.warrantyItemList = wiList2;
    //     ws.masterProductOld = war.Master_Product__r.Id;
    //     ws.saveWarrantyItem();

    //     //6、测试uploadReceiptAttachment方法
    //     ws.uploadReceiptAttachment(Blob.valueOf('ssssssss'), 'jepg');

    //     //7、测试relateWarrantyAndAttachment方法
    //     //******
    //     //ws.relateWarrantyAndAttachment();
    // }

    /**
     * @Author:     Zack
     * @Date:       2018-07-12
     * @Return:     
     * @Function:   测试WarrantyService的verifySerialNumber、getWarrantyByID、getWarrantySimpleByID、
     *              getWarrantyMapByIdSet、getWarrantyAndOrderById、getWarrantyAndOrderByIdSet、
     *              getWarrantyItemByWarrantyId、getWarrantyByWarrantyIds、getWarrantyByWarrantyName等方法
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   
     */
    static testMethod void testMethodsTwo(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Store_return_exchange_policy__c = Date.today().addDays(60);
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        //4、创建System Configuration数据
        System_Configuration__c sc = TestDataFactory.createSystemConfig('SN_Format');
        sc.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('System_Configuration__c', 'SN_Format');
        sc.RegExp__c = '^[ACU]([0-9]{2})((0[1-9])|(1[0-2]))[0-9]{5}$';
        sc.Name = 'EGO Skil SkilSaw';
        insert sc;
			
        Test.startTest();
        //================开始测试================
        //1、测试verifySerialNumber方法
        WarrantyService.verifySerialNumber('');
        WarrantyService.verifySerialNumber('NBM011506000001X');

        //2、测试verifySerialNumber方法
        WarrantyService.verifySerialNumber('EGO', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('Skil', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('Skil', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('SkilSaw', 'NBM011506000001X', null);

        //3、测试getWarrantyByID方法
        WarrantyService.getWarrantyByID(war.Id);
        WarrantyService.getWarrantyByID('');

        //4、测试getWarrantySimpleByID方法
        WarrantyService.getWarrantySimpleByID(war.Id);
        WarrantyService.getWarrantySimpleByIDforCase(war.Id);
        WarrantyService.getWarrantySimpleByID('');

        //5、测试getWarrantyMapByIdSet方法
        Set<String> warIdSet = new Set<String>();
        warIdSet.add(war.Id);
        WarrantyService.getWarrantyMapByIdSet(warIdSet);

        //6、测试getWarrantyAndOrderById方法
        WarrantyService.getWarrantyAndOrderById(war.Id);

        //7、测试getWarrantyAndOrderByIdSet方法
        WarrantyService.getWarrantyAndOrderByIdSet(warIdSet);

        //8、测试getWarrantyItemByWarrantyId方法
        WarrantyService.getWarrantyItemByWarrantyId(war.Id);

        //9、测试getWarrantyByWarrantyIds方法
        WarrantyService.getWarrantyByWarrantyIds(war.Id);

        //10、测试getWarrantyByWarrantyName方法
        WarrantyService.getWarrantyByWarrantyName(war.Name);

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的setWarrantyItemIndicator方法的各种分支
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testSetWarrantyItemIndicator(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Store_return_exchange_policy__c = Date.today().addDays(60);
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        war.Receipt_received_and_verified__c = true;
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        insert war;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
        wi.Serial_Number__c = 'TEST123456';
        wi.Expiration_Date__c = Date.today().addDays(30);
        insert wi;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi};

        // 测试有效保修情况
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.setWarrantyItemIndicator();
        System.assertEquals('Vailid Warranty', wi.Indicator__c, '应该设置为有效保修');

        // 测试过期保修情况
        wi.Expiration_Date__c = Date.today().addDays(-30);
        update wi;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Out of Warranty', wi.Indicator__c, '应该设置为过期保修');

        // 测试未知购买地情况
        wi.Expiration_Date__c = Date.today().addDays(30);
        update wi;
        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi.Indicator__c, '应该设置为待定状态');

        // 测试无序列号情况
        wi.Serial_Number__c = '';
        update wi;
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi.Indicator__c, '无序列号应该设置为待定状态');

        // 测试未验证发票情况
        wi.Serial_Number__c = 'TEST123456';
        update wi;
        war.Receipt_received_and_verified__c = false;
        update war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi.Indicator__c, '未验证发票应该设置为待定状态');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的countCost方法的更多分支
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testCountCostMoreBranches(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();
        pro.THD_store_warranty_period__c = 90;
        pro.Amazon_Store_warranty_period__c = 30;
        pro.Landed_Cost__c = 50;
        pro.Pick_up_Fee__c = 5;
        update pro;

        // 创建价格手册条目
        PricebookEntry pbe = new PricebookEntry();
        pbe.Pricebook2Id = Test.getStandardPricebookId();
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 100;
        insert pbe;

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today().addDays(-30);
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.masterProPBE = pbe;

        // 测试Home Depot购买地 - 90天内且替换成本>=商店政策成本
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        war.Purchase_Date__c = Date.today().addDays(-30);
        ws.countCost();
        System.assertEquals('Exchange the product via store.', war.Recommendation_of_Warranty_Issue__c,
                           'Home Depot 90天内应建议商店换货');

        // 测试Home Depot购买地 - 90天内且替换成本<商店政策成本
        pro.Landed_Cost__c = 10;
        update pro;
        ws.countCost();
        System.assertEquals('Send replacement to customer.', war.Recommendation_of_Warranty_Issue__c,
                           'Home Depot 90天内低成本应建议发送替换品');

        // 测试Amazon LLC购买地 - 30天内
        war.Place_of_Purchase_picklist__c = 'Amazon LLC';
        war.Purchase_Date__c = Date.today().addDays(-15);
        ws.countCost();
        System.assertEquals('Exchange the product via store.', war.Recommendation_of_Warranty_Issue__c,
                           'Amazon 30天内应建议商店换货');

        // 测试Ace Hardware购买地 - 30天内
        war.Place_of_Purchase_picklist__c = 'Ace Hardware';
        war.Purchase_Date__c = Date.today().addDays(-15);
        ws.countCost();
        System.assertEquals('Get exchange through dealer.', war.Recommendation_of_Warranty_Issue__c,
                           'Ace Hardware 30天内应建议经销商换货');

        // 测试Other购买地
        war.Place_of_Purchase_picklist__c = 'Other';
        ws.countCost();
        System.assertEquals('Repair via authorized repair center, recovering fee will be charged; Or purchase a new one.',
                           war.Recommendation_of_Warranty_Issue__c, 'Other购买地应建议付费维修');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的setStoreReturnExchangePolicy方法的完整分支
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testSetStoreReturnExchangePolicyComplete(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;

        // 测试Home Depot购买地
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(Date.today().addDays(90), war.Store_return_exchange_policy__c,
                           'Home Depot应设置90天退换货政策');

        // 测试Amazon LLC购买地
        war.Place_of_Purchase_picklist__c = 'Amazon LLC';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(Date.today().addDays(30), war.Store_return_exchange_policy__c,
                           'Amazon LLC应设置30天退换货政策');

        // 测试Ace Hardware购买地
        war.Place_of_Purchase_picklist__c = 'Ace Hardware';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(Date.today().addDays(30), war.Store_return_exchange_policy__c,
                           'Ace Hardware应设置30天退换货政策');

        // 测试其他购买地
        war.Place_of_Purchase_picklist__c = 'Other';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(null, war.Store_return_exchange_policy__c,
                           'Other购买地应设置为null');

        // 测试空购买日期
        war.Purchase_Date__c = null;
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(null, war.Store_return_exchange_policy__c,
                           '空购买日期应设置为null');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的setWarrantyItemExpirationDate方法的更多分支
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testSetWarrantyItemExpirationDateMoreBranches(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        war.Brand_Name__c = 'EGO';
        war.Product_Use_Type2__c = 'Residential';
        insert war;

        List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Product_Type__c = 'Product';
        wi1.Product_Model__c = '5';
        wiList.add(wi1);

        Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
        wi2.Product_Type__c = 'Battery';
        wiList.add(wi2);

        Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
        wi3.Product_Type__c = 'Charger';
        wiList.add(wi3);
        insert wiList;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = wiList;

        // 测试EGO品牌的过期日期设置
        ws.setWarrantyItemExpirationDate(true);

        // 验证不同产品类型的过期日期设置
        for(Warranty_Item__c item : ws.warrantyItemList) {
            System.assert(item.Expiration_Date__c != null, '过期日期应该被设置');
        }

        // 测试Professional/Commercial使用类型
        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.setWarrantyItemExpirationDate(false);

        // 测试空购买日期
        war.Purchase_Date__c = null;
        ws.setWarrantyItemExpirationDate(true);

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的uploadReceiptAttachment和relateWarrantyAndAttachment方法
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testAttachmentMethods(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;

        // 测试上传发票附件
        Blob testBlob = Blob.valueOf('测试发票内容');
        ws.uploadReceiptAttachment(testBlob, 'jpg');

        System.assert(ws.receiptAttachment != null, '发票附件应该被创建');
        System.assertEquals('jpg', ws.receiptAttachment.Name.substringAfterLast('.'), '文件扩展名应该正确');

        // 测试关联保修和附件
        if(ws.receiptAttachmentID != null) {
            ws.relateWarrantyAndAttachment();
        }

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的边界条件和异常处理
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testBoundaryConditionsAndExceptions(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();

        // 测试getWarrantyByWarrantyIds方法的null参数
        Warranty__c result1 = WarrantyService.getWarrantyByWarrantyIds(null);
        System.assert(result1 != null, '应该返回新的Warranty对象');

        // 测试getWarrantySimpleByID方法的空字符串参数
        Warranty__c result2 = WarrantyService.getWarrantySimpleByID('');
        System.assertEquals(null, result2, '空字符串应该返回null');

        // 测试getWarrantyAndOrderById方法的无效ID
        Warranty__c result3 = WarrantyService.getWarrantyAndOrderById('invalid_id');
        System.assertEquals(null, result3, '无效ID应该返回null');

        // 测试verifySerialNumber方法的各种品牌
        Boolean result4 = WarrantyService.verifySerialNumber('Unknown', 'TEST123', 'TESTCODE');
        System.assert(result4 != null, '应该返回验证结果');

        // 测试空集合的查询方法
        Set<String> emptySet = new Set<String>();
        Map<String, Warranty__c> result5 = WarrantyService.getWarrantyMapByIdSet(emptySet);
        System.assert(result5 != null, '应该返回空Map');

        List<Warranty__c> result6 = WarrantyService.getWarrantyAndOrderByIdSet(emptySet);
        System.assert(result6 != null, '应该返回空List');

        Map<String, Warranty__c> result7 = WarrantyService.getWarrantyNoItemsMapByIdSet(emptySet);
        System.assert(result7 != null, '应该返回空Map');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的产品类型映射功能
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testProductTypeMapping(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        war.Brand_Name__c = 'Skil';
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        insert war;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
        wi.Product_Type__c = 'Product';
        wi.Product_Model__c = '3';
        insert wi;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi};

        // 测试产品类型映射
        ws.setWarrantyItemExpirationDate(true);

        // 验证产品类型映射是否正确设置
        System.assert(ws.productTypeMap != null, '产品类型映射应该被初始化');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的verifySerialNumber方法的所有重载版本
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testVerifySerialNumberAllOverloads(){
        //================初始化数据================
        System_Configuration__c sc1 = new System_Configuration__c();
        sc1.Name = 'EGO_v4';
        sc1.RegExp__c = '^[A-Z]{3}[0-9]{11}[A-Z]$';
        sc1.Is_Active__c = true;
        sc1.Enabled__c = true;
        sc1.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('System_Configuration__c', 'SN_Format');
        insert sc1;

        System_Configuration__c sc2 = new System_Configuration__c();
        sc2.Name = 'FC_v2';
        sc2.RegExp__c = '^[A-Z]{2}[0-9]{10}$';
        sc2.Is_Active__c = true;
        sc2.Enabled__c = true;
        sc2.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('System_Configuration__c', 'SN_Format');
        insert sc2;

        Test.startTest();

        // 测试单参数verifySerialNumber方法
        Boolean result1 = WarrantyService.verifySerialNumber('ABC12345678901X');
        System.assert(result1 != null, '单参数验证应该返回结果');

        Boolean result2 = WarrantyService.verifySerialNumber('INVALID_SN');
        System.assert(result2 != null, '无效序列号验证应该返回结果');

        // 测试三参数verifySerialNumber方法
        Boolean result3 = WarrantyService.verifySerialNumber('EGO', 'ABC12345678901X', 'TEST_MODEL');
        System.assert(result3 != null, 'EGO品牌验证应该返回结果');

        Boolean result4 = WarrantyService.verifySerialNumber('Skil', 'ABC12345678901X', 'TEST_MODEL');
        System.assert(result4 != null, 'Skil品牌验证应该返回结果');

        Boolean result5 = WarrantyService.verifySerialNumber('SkilSaw', 'ABC12345678901X', 'TEST_MODEL');
        System.assert(result5 != null, 'SkilSaw品牌验证应该返回结果');

        Boolean result6 = WarrantyService.verifySerialNumber('Hammerhead', 'ABC12345678901X', 'TEST_MODEL');
        System.assert(result6 != null, 'Hammerhead品牌验证应该返回结果');

        Boolean result7 = WarrantyService.verifySerialNumber('Flex', 'ABC12345678901X', 'TEST_MODEL');
        System.assert(result7 != null, 'Flex品牌验证应该返回结果');

        Boolean result8 = WarrantyService.verifySerialNumber('Unknown', 'ABC12345678901X', 'TEST_MODEL');
        System.assert(result8 != null, '未知品牌验证应该返回结果');

        // 测试空品牌名
        Boolean result9 = WarrantyService.verifySerialNumber('', 'ABC12345678901X', 'TEST_MODEL');
        System.assertEquals(false, result9, '空品牌名应该返回false');

        Boolean result10 = WarrantyService.verifySerialNumber(null, 'ABC12345678901X', 'TEST_MODEL');
        System.assertEquals(false, result10, 'null品牌名应该返回false');

        // 测试四参数verifySerialNumber方法 - FC结尾的产品
        Boolean result11 = WarrantyService.verifySerialNumber('EGO', 'AB1234567890', 'TEST_MODEL-FC', false);
        System.assert(result11 != null, 'FC结尾产品验证应该返回结果');

        Boolean result12 = WarrantyService.verifySerialNumber('EGO', 'ABC12345678901X', 'TEST_MODEL-FC', false);
        System.assert(result12 != null, 'FC结尾产品EGO格式验证应该返回结果');

        // 测试编辑模式
        Boolean result13 = WarrantyService.verifySerialNumber('EGO', 'ABC12345678901X', 'TEST_MODEL-FC', true);
        System.assert(result13 != null, '编辑模式验证应该返回结果');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的saveWarrantyItem方法的更多场景
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testSaveWarrantyItemMoreScenarios(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;

        // 测试空的warranty item列表
        ws.warrantyItemList = new List<Warranty_Item__c>();
        ws.saveWarrantyItem();

        // 测试单个warranty item
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Serial_Number__c = 'TEST123456';
        insert wi1;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi1};
        ws.saveWarrantyItem();

        // 测试多个warranty items
        Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
        wi2.Serial_Number__c = 'TEST789012';
        insert wi2;
        Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
        wi3.Serial_Number__c = 'TEST345678';
        insert wi3;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi1, wi2, wi3};
        ws.saveWarrantyItem();

        // 测试有masterProductOld的情况
        ws.masterProductOld = pro.Id;
        ws.warrantyItemOldList = new List<Warranty_Item__c>{wi1};
        ws.saveWarrantyItem();

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的查询方法的更多边界条件
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率
     */
    static testMethod void testQueryMethodsMoreBoundaryConditions(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
        wi.Indicator__c = 'Vailid Warranty';
        insert wi;

        Test.startTest();

        // 测试getWarrantyByID方法的各种参数
        Warranty__c result1 = WarrantyService.getWarrantyByID(war.Id);
        System.assert(result1 != null, '有效ID应该返回结果');

        Warranty__c result2 = WarrantyService.getWarrantyByID('invalid_id');
        System.assertEquals(null, result2, '无效ID应该返回null');

        Warranty__c result3 = WarrantyService.getWarrantyByID('');
        System.assertEquals(null, result3, '空ID应该返回null');

        // 测试getWarrantySimpleByIDforCase方法
        Warranty__c result4 = WarrantyService.getWarrantySimpleByIDforCase(war.Id);
        System.assert(result4 != null, '有效ID应该返回结果');

        // 测试getWarrantyItemByWarrantyId方法
        List<Warranty_Item__c> result5 = WarrantyService.getWarrantyItemByWarrantyId(war.Id);
        System.assert(result5 != null, '应该返回warranty items');

        List<Warranty_Item__c> result6 = WarrantyService.getWarrantyItemByWarrantyId('invalid_id');
        System.assert(result6 != null, '无效ID应该返回空列表');

        // 测试getWarrantyByWarrantyName方法
        Warranty__c result7 = WarrantyService.getWarrantyByWarrantyName(war.Name);
        System.assert(result7 != null, '有效名称应该返回结果');

        Warranty__c result8 = WarrantyService.getWarrantyByWarrantyName('INVALID_NAME');
        System.assert(result8 != null, '无效名称应该返回空对象');

        // 测试集合查询方法的边界条件
        Set<String> singleIdSet = new Set<String>{war.Id};
        Map<String, Warranty__c> result9 = WarrantyService.getWarrantyMapByIdSet(singleIdSet);
        System.assert(result9 != null && result9.size() > 0, '单个ID集合应该返回结果');

        List<Warranty__c> result10 = WarrantyService.getWarrantyAndOrderByIdSet(singleIdSet);
        System.assert(result10 != null, '应该返回warranty列表');

        Map<String, Warranty__c> result11 = WarrantyService.getWarrantyNoItemsMapByIdSet(singleIdSet);
        System.assert(result11 != null, '应该返回warranty映射');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的setWarrantyItemIndicator方法的完整分支覆盖
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%以上
     */
    static testMethod void testSetWarrantyItemIndicatorCompleteBranches(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        war.Receipt_received_and_verified__c = true;
        war.Lost_Receipt__c = false;
        war.Pending__c = false;
        insert war;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
        wi.Serial_Number__c = 'TEST123456';
        wi.Expiration_Date__c = Date.today().addDays(30);
        insert wi;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi};

        // 测试所有可能的指示器状态组合

        // 1. 测试有效保修 - 授权购买地，未过期，有序列号，发票已验证
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.setWarrantyItemIndicator();
        System.assertEquals('Vailid Warranty', wi.Indicator__c, '应该设置为有效保修');

        // 2. 测试过期保修
        wi.Expiration_Date__c = Date.today().addDays(-30);
        update wi;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Out of Warranty', wi.Indicator__c, '应该设置为过期保修');

        // 3. 测试未知购买地 - 未过期
        wi.Expiration_Date__c = Date.today().addDays(30);
        update wi;
        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi.Indicator__c, '未知购买地应该设置为待定');

        // 4. 测试无序列号 - 授权购买地，未过期，发票已验证
        wi.Serial_Number__c = '';
        update wi;
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi.Indicator__c, '无序列号应该设置为待定');

        // 5. 测试发票未验证 - 授权购买地，未过期，有序列号
        wi.Serial_Number__c = 'TEST123456';
        update wi;
        war.Receipt_received_and_verified__c = false;
        update war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi.Indicator__c, '发票未验证应该设置为待定');

        // 6. 测试Lost_Receipt为true的情况
        war.Lost_Receipt__c = true;
        update war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Lost Receipt', wi.Indicator__c, '丢失发票应该设置为Lost Receipt');

        // 7. 测试未授权购买地
        war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        update war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Out of Warranty', wi.Indicator__c, '未授权购买地应该设置为过期保修');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的countCost方法的所有购买地分支
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%以上
     */
    static testMethod void testCountCostAllPurchasePlaceBranches(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();
        pro.THD_store_warranty_period__c = 90;
        pro.Amazon_Store_warranty_period__c = 30;
        pro.Landed_Cost__c = 50;
        pro.Pick_up_Fee__c = 5;
        update pro;

        PricebookEntry pbe = new PricebookEntry();
        pbe.Pricebook2Id = Test.getStandardPricebookId();
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 100;
        insert pbe;

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today().addDays(-30);
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.masterProPBE = pbe;

        // 测试所有购买地的费用计算

        // 1. 测试Unauthorized Dealer
        war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        ws.countCost();
        System.assertEquals('Return to place of purchase.', war.Recommendation_of_Warranty_Issue__c,
                           '未授权经销商应建议返回购买地');

        // 2. 测试Home Depot - 各种时间和成本组合
        war.Place_of_Purchase_picklist__c = 'Home Depot';

        // 2a. 90天内，替换成本 >= 商店政策成本
        war.Purchase_Date__c = Date.today().addDays(-30);
        pro.Landed_Cost__c = 200; // 高成本
        update pro;
        ws.countCost();
        System.assertEquals('Exchange the product via store.', war.Recommendation_of_Warranty_Issue__c,
                           'Home Depot 90天内高成本应建议商店换货');

        // 2b. 90天内，替换成本 < 商店政策成本
        pro.Landed_Cost__c = 10; // 低成本
        update pro;
        ws.countCost();
        System.assertEquals('Send replacement to customer.', war.Recommendation_of_Warranty_Issue__c,
                           'Home Depot 90天内低成本应建议发送替换品');

        // 2c. 90天后，EGO保修期内
        war.Purchase_Date__c = Date.today().addDays(-120);
        ws.countCost();
        System.assertEquals('Repair via authorized repair center freely.', war.Recommendation_of_Warranty_Issue__c,
                           'Home Depot EGO保修期内应建议免费维修');

        // 2d. EGO保修期外
        war.Purchase_Date__c = Date.today().addDays(-400);
        ws.countCost();
        System.assertEquals('Repair via authorized repair center, recovering fee will be charged; Or purchase a new one.',
                           war.Recommendation_of_Warranty_Issue__c, 'Home Depot EGO保修期外应建议付费维修');

        // 3. 测试Amazon LLC
        war.Place_of_Purchase_picklist__c = 'Amazon LLC';
        war.Purchase_Date__c = Date.today().addDays(-15);
        pro.Landed_Cost__c = 200; // 高成本
        update pro;
        ws.countCost();
        System.assertEquals('Exchange the product via store.', war.Recommendation_of_Warranty_Issue__c,
                           'Amazon 30天内应建议商店换货');

        // 4. 测试Ace Hardware
        war.Place_of_Purchase_picklist__c = 'Ace Hardware';
        war.Purchase_Date__c = Date.today().addDays(-15);
        ws.countCost();
        System.assertEquals('Get exchange through dealer.', war.Recommendation_of_Warranty_Issue__c,
                           'Ace Hardware 30天内应建议经销商换货');

        // 5. 测试Other购买地
        war.Place_of_Purchase_picklist__c = 'Other';
        ws.countCost();
        System.assertEquals('Repair via authorized repair center, recovering fee will be charged; Or purchase a new one.',
                           war.Recommendation_of_Warranty_Issue__c, 'Other购买地应建议付费维修');

        // 6. 测试未知购买地
        war.Place_of_Purchase_picklist__c = 'Unknown Store';
        ws.countCost();
        System.assertEquals('Repair via authorized repair center, recovering fee will be charged; Or purchase a new one.',
                           war.Recommendation_of_Warranty_Issue__c, '未知购买地应建议付费维修');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-01-01
     * @Return:
     * @Function:   测试WarrantyService的setWarrantyItemExpirationDate方法的所有品牌分支
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%以上
     */
    static testMethod void testSetWarrantyItemExpirationDateAllBrands(){
        //================初始化数据================
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        Product2 pro = TestDataFactory.createProduct();

        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();

        // 创建不同类型的warranty items
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Product_Type__c = 'Product';
        wi1.Product_Model__c = '3';
        wiList.add(wi1);

        Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
        wi2.Product_Type__c = 'Battery';
        wi2.Product_Model__c = '5';
        wiList.add(wi2);

        Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
        wi3.Product_Type__c = 'Charger';
        wi3.Product_Model__c = '2';
        wiList.add(wi3);

        insert wiList;

        Test.startTest();
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = wiList;

        // 测试所有品牌的过期日期设置

        // 1. 测试Skil品牌 - Residential使用类型
        war.Brand_Name__c = 'Skil';
        war.Product_Use_Type2__c = 'Residential';
        ws.setWarrantyItemExpirationDate(true);

        // 2. 测试Skil品牌 - Professional/Commercial使用类型
        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.setWarrantyItemExpirationDate(false);

        // 3. 测试SkilSaw品牌 - Residential使用类型
        war.Brand_Name__c = 'SkilSaw';
        war.Product_Use_Type2__c = 'Residential';
        ws.setWarrantyItemExpirationDate(true);

        // 4. 测试SkilSaw品牌 - Professional/Commercial使用类型
        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.setWarrantyItemExpirationDate(false);

        // 5. 测试EGO品牌 - Residential使用类型
        war.Brand_Name__c = 'EGO';
        war.Product_Use_Type2__c = 'Residential';
        ws.setWarrantyItemExpirationDate(true);

        // 6. 测试EGO品牌 - Professional/Commercial使用类型
        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.setWarrantyItemExpirationDate(false);

        // 7. 测试其他品牌
        war.Brand_Name__c = 'Other';
        war.Product_Use_Type2__c = 'Residential';
        ws.setWarrantyItemExpirationDate(true);

        // 8. 测试空购买日期
        war.Purchase_Date__c = null;
        ws.setWarrantyItemExpirationDate(false);

        // 验证过期日期是否被正确设置
        for(Warranty_Item__c item : ws.warrantyItemList) {
            System.assert(item.Expiration_Date__c != null || war.Purchase_Date__c == null,
                         '过期日期应该被设置（除非购买日期为空）');
        }

        Test.stopTest();
    }
}